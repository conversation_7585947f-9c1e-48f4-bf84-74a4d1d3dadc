# 地图系统开发说明

## 概述
基于二维格子地图的SLG游戏地图系统，实现5000×5000的大型地图，支持地形系统、资源动态刷新、九宫格渲染优化等核心功能。

## 功能需求

### 1. 地图基础设定
- **地图尺寸**：5000×5000格子（可根据测试情况调整）
- **坐标系统**：二维坐标，x和y均为整数，范围0-4999
- **格子大小**：适配手机屏幕触控操作（建议40×40像素）
- **渲染优化**：九宫格渲染机制，以当前视野为中心渲染周围8屏
- **屏幕标准**：以1080×1920分辨率为基准

### 2. 地形系统
- **地形等级**：1-10级地形分布
- **地形类型**：10种不同地形类型
- **静态分布**：开服时初始化，统一前不再改变
- **影响机制**：影响兵种行军速度、战斗相生相克、策略应用

#### 地形类型列表
1. 平原 - 基础地形，无特殊效果
2. 草地 - 轻骑兵移动加成
3. 森林 - 弓兵防御加成，视野受限
4. 雪地 - 移动速度减缓，补给消耗增加
5. 湿地 - 重装兵种移动困难
6. 沼泽 - 陷阱效果，移动大幅减缓
7. 山地 - 防御加成，攻城器械无法通过
8. 荒漠 - 补给消耗增加，水源稀缺
9. 河流 - 需要渡河，移动受限
10. 栈道 - 快速通道，但易受攻击

### 3. 资源系统
- **动态刷新**：资源随机刷新在地图格子上
- **总量控制**：固定总量，取走后延迟5-10分钟随机位置刷新
- **等级限制**：资源等级与格子等级对应
- **刷新机制**：避免同时大量刷新，降低服务器压力

#### 资源类型
1. **矿石**：产出矿石资源，用于建筑和装备
2. **森林**：产出木头资源，用于建筑和器械
3. **动物**：产出食物资源，维持军队补给
4. **流寇**：产出战国币及各种物品，需要战斗获取
5. **九鼎**：特殊资源，全服仅9个，等级固定为10级

#### 资源等级分布
- **1-9级资源**：各类型都有对应等级
- **10级资源**：仅九鼎为10级
- **资源形态**：同类型资源有不同外观（如动物：羊、马、牛）

## 数据库设计

### 地图格子表 (map_tiles)
```sql
CREATE TABLE map_tiles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    x INT NOT NULL COMMENT 'X坐标',
    y INT NOT NULL COMMENT 'Y坐标',
    terrain_type ENUM('plain', 'grassland', 'forest', 'snow', 'wetland', 'swamp', 'mountain', 'desert', 'river', 'bridge') NOT NULL COMMENT '地形类型',
    terrain_level TINYINT UNSIGNED NOT NULL COMMENT '地形等级(1-10)',
    country_id BIGINT UNSIGNED NULL COMMENT '所属国家ID',
    is_occupied BOOLEAN DEFAULT FALSE COMMENT '是否被占领',
    occupied_by_user_id BIGINT UNSIGNED NULL COMMENT '占领者用户ID',
    occupied_at TIMESTAMP NULL COMMENT '占领时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_coordinate (x, y),
    INDEX idx_terrain_type (terrain_type),
    INDEX idx_terrain_level (terrain_level),
    INDEX idx_country_id (country_id),
    INDEX idx_coordinates (x, y),
    INDEX idx_occupied (is_occupied),
    FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE SET NULL,
    FOREIGN KEY (occupied_by_user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

### 地图资源表 (map_resources)
```sql
CREATE TABLE map_resources (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    x INT NOT NULL COMMENT 'X坐标',
    y INT NOT NULL COMMENT 'Y坐标',
    resource_type ENUM('ore', 'wood', 'food', 'bandit', 'ding') NOT NULL COMMENT '资源类型',
    resource_level TINYINT UNSIGNED NOT NULL COMMENT '资源等级(1-10)',
    resource_amount INT UNSIGNED NOT NULL COMMENT '资源数量',
    max_amount INT UNSIGNED NOT NULL COMMENT '最大资源量',
    appearance_type VARCHAR(20) NOT NULL COMMENT '外观类型',
    is_collected BOOLEAN DEFAULT FALSE COMMENT '是否被采集完',
    collected_by_user_id BIGINT UNSIGNED NULL COMMENT '采集者用户ID',
    collected_at TIMESTAMP NULL COMMENT '采集完成时间',
    respawn_at TIMESTAMP NULL COMMENT '重新刷新时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_coordinates (x, y),
    INDEX idx_resource_type (resource_type),
    INDEX idx_resource_level (resource_level),
    INDEX idx_is_collected (is_collected),
    INDEX idx_respawn_at (respawn_at),
    FOREIGN KEY (collected_by_user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

### 九鼎表 (nine_dings)
```sql
CREATE TABLE nine_dings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    ding_type ENUM('ji', 'yan', 'qing', 'xu', 'yang', 'jing', 'yu', 'liang', 'yong') UNIQUE NOT NULL COMMENT '鼎的类型',
    name VARCHAR(20) NOT NULL COMMENT '鼎的名称',
    x INT NULL COMMENT 'X坐标',
    y INT NULL COMMENT 'Y坐标',
    is_spawned BOOLEAN DEFAULT FALSE COMMENT '是否已刷新',
    controlled_by_user_id BIGINT UNSIGNED NULL COMMENT '控制者用户ID',
    controlled_by_country_id BIGINT UNSIGNED NULL COMMENT '控制国家ID',
    controlled_at TIMESTAMP NULL COMMENT '控制时间',
    special_effect JSON COMMENT '特殊效果配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_coordinates (x, y),
    INDEX idx_is_spawned (is_spawned),
    INDEX idx_controlled_by_user (controlled_by_user_id),
    INDEX idx_controlled_by_country (controlled_by_country_id),
    FOREIGN KEY (controlled_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (controlled_by_country_id) REFERENCES countries(id) ON DELETE SET NULL
);
```

### 资源刷新配置表 (resource_spawn_config)
```sql
CREATE TABLE resource_spawn_config (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    resource_type ENUM('ore', 'wood', 'food', 'bandit', 'ding') NOT NULL,
    resource_level TINYINT UNSIGNED NOT NULL,
    max_count INT UNSIGNED NOT NULL COMMENT '最大数量',
    current_count INT UNSIGNED DEFAULT 0 COMMENT '当前数量',
    respawn_min_delay INT UNSIGNED DEFAULT 300 COMMENT '最小刷新延迟(秒)',
    respawn_max_delay INT UNSIGNED DEFAULT 600 COMMENT '最大刷新延迟(秒)',
    terrain_restrictions JSON COMMENT '地形限制',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_resource_level (resource_type, resource_level),
    INDEX idx_resource_type (resource_type)
);
```

## 后端API设计

### 1. 获取地图区域信息
```
GET /api/map/region?x={centerX}&y={centerY}&radius={radius}
Authorization: Bearer {token}
```

**响应：**
```json
{
    "status": "success",
    "data": {
        "center": {"x": 2500, "y": 2500},
        "radius": 50,
        "tiles": [
            {
                "x": 2500,
                "y": 2500,
                "terrain_type": "plain",
                "terrain_level": 5,
                "country_id": 1,
                "is_occupied": false,
                "has_resource": true,
                "resource": {
                    "type": "ore",
                    "level": 3,
                    "amount": 1500,
                    "appearance": "iron_mine"
                }
            }
        ],
        "total_tiles": 2500
    }
}
```

### 2. 获取九宫格地图数据
```
GET /api/map/nine-grid?x={centerX}&y={centerY}&screen_width={width}&screen_height={height}
Authorization: Bearer {token}
```

**响应：**
```json
{
    "status": "success",
    "data": {
        "center_screen": {"x": 2500, "y": 2500},
        "screen_size": {"width": 1080, "height": 1920},
        "tile_size": 40,
        "grids": [
            {
                "screen_position": "center",
                "bounds": {
                    "min_x": 2487, "max_x": 2513,
                    "min_y": 2476, "max_y": 2524
                },
                "tiles": []
            }
        ]
    }
}
```

### 3. 获取资源信息
```
GET /api/map/resources?x={x}&y={y}
Authorization: Bearer {token}
```

**响应：**
```json
{
    "status": "success",
    "data": {
        "coordinate": {"x": 2500, "y": 2500},
        "resource": {
            "id": 12345,
            "type": "ore",
            "level": 5,
            "amount": 2000,
            "max_amount": 2000,
            "appearance": "gold_mine",
            "collection_time": 3600,
            "required_troops": 100
        }
    }
}
```

### 4. 采集资源
```
POST /api/map/collect-resource
Authorization: Bearer {token}
```

**请求参数：**
```json
{
    "resource_id": 12345,
    "troop_count": 100,
    "collection_time": 3600
}
```

**响应：**
```json
{
    "status": "success",
    "message": "开始采集资源",
    "data": {
        "collection_id": 67890,
        "estimated_completion": "2025-07-11 15:30:00",
        "estimated_yield": 1800
    }
}
```

### 5. 获取九鼎状态
```
GET /api/map/nine-dings
Authorization: Bearer {token}
```

**响应：**
```json
{
    "status": "success",
    "data": {
        "dings": [
            {
                "type": "qian",
                "name": "乾鼎",
                "position": {"x": 1000, "y": 1000},
                "is_spawned": true,
                "controlled_by": {
                    "user_id": 123,
                    "username": "项羽",
                    "country": "chu"
                },
                "controlled_at": "2025-07-10 20:00:00",
                "special_effect": {
                    "type": "military_boost",
                    "value": 20
                }
            }
        ]
    }
}
```

## 前端实现

### 1. 地图相关类型定义
```typescript
// types/map.ts
export interface MapTile {
    x: number
    y: number
    terrainType: TerrainType
    terrainLevel: number
    countryId?: number
    isOccupied: boolean
    occupiedByUserId?: number
    occupiedAt?: string
    hasResource: boolean
    resource?: MapResource
}

export type TerrainType = 'plain' | 'grassland' | 'forest' | 'snow' | 'wetland' |
                         'swamp' | 'mountain' | 'desert' | 'river' | 'bridge'

export interface MapResource {
    id: number
    type: ResourceType
    level: number
    amount: number
    maxAmount: number
    appearance: string
    collectionTime: number
    requiredTroops: number
}

export type ResourceType = 'ore' | 'wood' | 'food' | 'bandit' | 'ding'

export interface MapCoordinate {
    x: number
    y: number
}

export interface NineDing {
    type: DingType
    name: string
    position: MapCoordinate | null
    isSpawned: boolean
    controlledBy?: {
        userId: number
        username: string
        country: string
    }
    controlledAt?: string
    specialEffect: {
        type: string
        value: number
    }
}

export type DingType = 'ji' | 'yan' | 'qing' | 'xu' | 'yang' | 'jing' | 'yu' | 'liang' | 'yong'

export interface MapViewport {
    centerX: number
    centerY: number
    screenWidth: number
    screenHeight: number
    tileSize: number
    visibleTiles: MapTile[]
}
```

### 2. 地图状态管理
```typescript
// stores/map.ts
export interface MapState {
    viewport: MapViewport
    currentTiles: MapTile[]
    nineGrids: MapGrid[]
    nineD ings: NineDing[]
    selectedTile: MapTile | null
    isLoading: boolean
    lastUpdateTime: number
}

export interface MapGrid {
    screenPosition: string
    bounds: {
        minX: number
        maxX: number
        minY: number
        maxY: number
    }
    tiles: MapTile[]
}

export const useMapStore = defineStore('map', {
    state: (): MapState => ({
        viewport: {
            centerX: 2500,
            centerY: 2500,
            screenWidth: 1080,
            screenHeight: 1920,
            tileSize: 40,
            visibleTiles: []
        },
        currentTiles: [],
        nineGrids: [],
        nineD ings: [],
        selectedTile: null,
        isLoading: false,
        lastUpdateTime: 0
    }),

    getters: {
        getTileAt: (state) => (x: number, y: number) =>
            state.currentTiles.find(tile => tile.x === x && tile.y === y),

        getVisibleBounds: (state) => {
            const { centerX, centerY, screenWidth, screenHeight, tileSize } = state.viewport
            const tilesPerScreenX = Math.ceil(screenWidth / tileSize)
            const tilesPerScreenY = Math.ceil(screenHeight / tileSize)

            return {
                minX: centerX - Math.floor(tilesPerScreenX * 1.5),
                maxX: centerX + Math.floor(tilesPerScreenX * 1.5),
                minY: centerY - Math.floor(tilesPerScreenY * 1.5),
                maxY: centerY + Math.floor(tilesPerScreenY * 1.5)
            }
        },

        getResourcesByType: (state) => (type: ResourceType) =>
            state.currentTiles
                .filter(tile => tile.hasResource && tile.resource?.type === type)
                .map(tile => tile.resource!)
    },

    actions: {
        async loadMapRegion(centerX: number, centerY: number, radius: number),
        async loadNineGrids(centerX: number, centerY: number),
        async collectResource(resourceId: number, troopCount: number),
        async loadNineD ings(),
        updateViewport(centerX: number, centerY: number),
        selectTile(x: number, y: number)
    }
})
```

### 3. 地图渲染组件
```vue
<!-- components/MapCanvas.vue -->
<template>
    <div class="map-container" ref="mapContainer">
        <canvas
            ref="mapCanvas"
            :width="canvasWidth"
            :height="canvasHeight"
            @touchstart="handleTouchStart"
            @touchmove="handleTouchMove"
            @touchend="handleTouchEnd"
            @click="handleTileClick"
        />

        <!-- 地图UI层 -->
        <div class="map-ui-overlay">
            <MapMinimap />
            <MapControls />
            <TileInfoPanel v-if="selectedTile" :tile="selectedTile" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useMapStore } from '@/stores/map'
import type { MapTile } from '@/types/map'

const mapStore = useMapStore()
const mapContainer = ref<HTMLDivElement>()
const mapCanvas = ref<HTMLCanvasElement>()

const canvasWidth = ref(1080)
const canvasHeight = ref(1920)

// 地图渲染逻辑
const renderMap = () => {
    const canvas = mapCanvas.value
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

    // 渲染地形
    renderTerrain(ctx)

    // 渲染资源
    renderResources(ctx)

    // 渲染占领标记
    renderOccupation(ctx)
}

const renderTerrain = (ctx: CanvasRenderingContext2D) => {
    mapStore.currentTiles.forEach(tile => {
        const screenPos = worldToScreen(tile.x, tile.y)
        if (isInViewport(screenPos)) {
            drawTerrain(ctx, tile, screenPos)
        }
    })
}

const renderResources = (ctx: CanvasRenderingContext2D) => {
    mapStore.currentTiles
        .filter(tile => tile.hasResource)
        .forEach(tile => {
            const screenPos = worldToScreen(tile.x, tile.y)
            if (isInViewport(screenPos)) {
                drawResource(ctx, tile.resource!, screenPos)
            }
        })
}

// 坐标转换
const worldToScreen = (worldX: number, worldY: number) => {
    const { centerX, centerY, tileSize } = mapStore.viewport
    return {
        x: (worldX - centerX) * tileSize + canvasWidth.value / 2,
        y: (worldY - centerY) * tileSize + canvasHeight.value / 2
    }
}

const screenToWorld = (screenX: number, screenY: number) => {
    const { centerX, centerY, tileSize } = mapStore.viewport
    return {
        x: Math.floor((screenX - canvasWidth.value / 2) / tileSize + centerX),
        y: Math.floor((screenY - canvasHeight.value / 2) / tileSize + centerY)
    }
}

// 触控处理
const handleTileClick = (event: MouseEvent) => {
    const rect = mapCanvas.value?.getBoundingClientRect()
    if (!rect) return

    const screenX = event.clientX - rect.left
    const screenY = event.clientY - rect.top
    const worldPos = screenToWorld(screenX, screenY)

    mapStore.selectTile(worldPos.x, worldPos.y)
}

// 监听地图数据变化
watch(() => mapStore.currentTiles, renderMap, { deep: true })
watch(() => mapStore.viewport, renderMap, { deep: true })

onMounted(() => {
    renderMap()
})
</script>
```

### 4. 地形配置管理
```typescript
// config/terrain.ts
export const TERRAIN_CONFIG = {
    plain: {
        name: '平原',
        color: '#90EE90',
        movementModifier: 1.0,
        defenseModifier: 1.0,
        description: '基础地形，无特殊效果'
    },
    grassland: {
        name: '草地',
        color: '#7CFC00',
        movementModifier: 1.1,
        defenseModifier: 0.9,
        description: '轻骑兵移动加成+10%'
    },
    forest: {
        name: '森林',
        color: '#228B22',
        movementModifier: 0.8,
        defenseModifier: 1.2,
        description: '弓兵防御加成+20%，移动速度-20%'
    },
    snow: {
        name: '雪地',
        color: '#F0F8FF',
        movementModifier: 0.7,
        defenseModifier: 1.0,
        description: '移动速度-30%，补给消耗+50%'
    },
    wetland: {
        name: '湿地',
        color: '#8FBC8F',
        movementModifier: 0.6,
        defenseModifier: 1.1,
        description: '重装兵种移动困难-40%'
    },
    swamp: {
        name: '沼泽',
        color: '#556B2F',
        movementModifier: 0.4,
        defenseModifier: 1.3,
        description: '陷阱效果，移动速度-60%'
    },
    mountain: {
        name: '山地',
        color: '#A0522D',
        movementModifier: 0.5,
        defenseModifier: 1.5,
        description: '防御加成+50%，攻城器械无法通过'
    },
    desert: {
        name: '荒漠',
        color: '#F4A460',
        movementModifier: 0.8,
        defenseModifier: 0.9,
        description: '补给消耗+100%，水源稀缺'
    },
    river: {
        name: '河流',
        color: '#4169E1',
        movementModifier: 0.3,
        defenseModifier: 1.2,
        description: '需要渡河，移动受限-70%'
    },
    bridge: {
        name: '栈道',
        color: '#8B4513',
        movementModifier: 1.5,
        defenseModifier: 0.7,
        description: '快速通道+50%，但防御-30%'
    }
}

export const RESOURCE_CONFIG = {
    ore: {
        name: '矿石',
        color: '#C0C0C0',
        appearances: ['iron_mine', 'copper_mine', 'gold_mine', 'silver_mine'],
        baseYield: 100,
        collectionTime: 3600
    },
    wood: {
        name: '森林',
        color: '#8B4513',
        appearances: ['oak_forest', 'pine_forest', 'bamboo_grove'],
        baseYield: 120,
        collectionTime: 2400
    },
    food: {
        name: '动物',
        color: '#DEB887',
        appearances: ['sheep', 'cattle', 'horse', 'deer'],
        baseYield: 80,
        collectionTime: 1800
    },
    bandit: {
        name: '流寇',
        color: '#8B0000',
        appearances: ['bandit_camp', 'rebel_fort', 'outlaw_den'],
        baseYield: 200,
        collectionTime: 7200
    },
    ding: {
        name: '九鼎',
        color: '#FFD700',
        appearances: ['ancient_ding'],
        baseYield: 0,
        collectionTime: 0
    }
}
```

## 业务逻辑流程

### 1. 地图初始化流程
```mermaid
graph TD
    A[服务器启动] --> B[读取地形配置]
    B --> C[生成5000x5000地图]
    C --> D[分配地形类型]
    D --> E[设置地形等级]
    E --> F[分配国家领土]
    F --> G[初始化资源刷新点]
    G --> H[生成九鼎位置]
    H --> I[地图初始化完成]
```

### 2. 资源刷新流程
```mermaid
graph TD
    A[资源被采集] --> B[更新资源状态]
    B --> C[计算刷新延迟]
    C --> D[添加到刷新队列]
    D --> E[等待刷新时间]
    E --> F[检查总量限制]
    F -->|未达上限| G[随机选择刷新位置]
    F -->|已达上限| H[等待其他资源被采集]
    G --> I[检查地形等级匹配]
    I -->|匹配| J[刷新资源]
    I -->|不匹配| G
    J --> K[通知附近玩家]
```

### 3. 九宫格渲染流程
```mermaid
graph TD
    A[玩家移动视野] --> B[计算新的中心坐标]
    B --> C[确定九宫格范围]
    C --> D[检查缓存数据]
    D -->|有缓存| E[使用缓存数据]
    D -->|无缓存| F[请求服务器数据]
    F --> G[加载地形数据]
    G --> H[加载资源数据]
    H --> I[缓存数据]
    I --> E
    E --> J[渲染地图]
    J --> K[更新UI显示]
```

## 配置管理

### 1. 地图系统配置
```php
// config/map.php
return [
    'map_size' => [
        'width' => 5000,
        'height' => 5000,
        'tile_size' => 40, // 像素
    ],

    'viewport' => [
        'screen_width' => 1080,
        'screen_height' => 1920,
        'render_radius' => 2, // 九宫格半径
    ],

    'terrain' => [
        'level_distribution' => [
            1 => 0.20, // 20%的1级地形
            2 => 0.18,
            3 => 0.16,
            4 => 0.14,
            5 => 0.12,
            6 => 0.10,
            7 => 0.06,
            8 => 0.03,
            9 => 0.01,
            10 => 0.001, // 0.1%的10级地形
        ],

        'type_distribution' => [
            'plain' => 0.25,
            'grassland' => 0.20,
            'forest' => 0.15,
            'mountain' => 0.10,
            'desert' => 0.08,
            'river' => 0.07,
            'snow' => 0.05,
            'wetland' => 0.04,
            'swamp' => 0.03,
            'bridge' => 0.03,
        ],
    ],

    'resources' => [
        'total_limits' => [
            'ore' => [1 => 1000, 2 => 800, 3 => 600, 4 => 400, 5 => 200, 6 => 100, 7 => 50, 8 => 25, 9 => 10],
            'wood' => [1 => 1200, 2 => 900, 3 => 700, 4 => 500, 5 => 250, 6 => 120, 7 => 60, 8 => 30, 9 => 12],
            'food' => [1 => 1500, 2 => 1100, 3 => 800, 4 => 600, 5 => 300, 6 => 150, 7 => 75, 8 => 35, 9 => 15],
            'bandit' => [1 => 500, 2 => 400, 3 => 300, 4 => 200, 5 => 100, 6 => 50, 7 => 25, 8 => 12, 9 => 5],
            'ding' => [10 => 9], // 九鼎固定9个
        ],

        'respawn_delays' => [
            'min' => 300, // 5分钟
            'max' => 600, // 10分钟
        ],

        'level_terrain_mapping' => [
            1 => [1, 2],
            2 => [1, 2, 3],
            3 => [2, 3, 4],
            4 => [3, 4, 5],
            5 => [4, 5, 6],
            6 => [5, 6, 7],
            7 => [6, 7, 8],
            8 => [7, 8, 9],
            9 => [8, 9, 10],
            10 => [10], // 10级资源只在10级地形
        ],
    ],

    'nine_dings' => [
        'qian' => ['name' => '冀y鼎', 'effect' => 'military_boost', 'value' => 20],
        'kun' => ['name' => '坤鼎', 'effect' => 'resource_boost', 'value' => 15],
        'zhen' => ['name' => '震鼎', 'effect' => 'speed_boost', 'value' => 25],
        'xun' => ['name' => '巽鼎', 'effect' => 'trade_boost', 'value' => 30],
        'kan' => ['name' => '坎鼎', 'effect' => 'defense_boost', 'value' => 20],
        'li' => ['name' => '离鼎', 'effect' => 'attack_boost', 'value' => 25],
        'gen' => ['name' => '艮鼎', 'effect' => 'construction_boost', 'value' => 40],
        'dui' => ['name' => '兑鼎', 'effect' => 'diplomacy_boost', 'value' => 35],
        'center' => ['name' => '中央鼎', 'effect' => 'all_boost', 'value' => 10],
    ],
];
```

## 错误处理

### 1. 地图系统错误码
```typescript
export enum MapErrorCode {
    COORDINATE_OUT_OF_BOUNDS = 'COORDINATE_OUT_OF_BOUNDS',
    TILE_NOT_FOUND = 'TILE_NOT_FOUND',
    RESOURCE_NOT_AVAILABLE = 'RESOURCE_NOT_AVAILABLE',
    RESOURCE_ALREADY_COLLECTED = 'RESOURCE_ALREADY_COLLECTED',
    INSUFFICIENT_TROOPS = 'INSUFFICIENT_TROOPS',
    INVALID_TERRAIN_TYPE = 'INVALID_TERRAIN_TYPE',
    DING_NOT_SPAWNED = 'DING_NOT_SPAWNED',
    DING_ALREADY_CONTROLLED = 'DING_ALREADY_CONTROLLED',
    MAP_DATA_CORRUPTED = 'MAP_DATA_CORRUPTED'
}
```

### 2. 错误提示文案
```typescript
export const mapErrorMessages = {
    COORDINATE_OUT_OF_BOUNDS: '坐标超出地图范围',
    TILE_NOT_FOUND: '地图格子不存在',
    RESOURCE_NOT_AVAILABLE: '资源不可用',
    RESOURCE_ALREADY_COLLECTED: '资源已被采集',
    INSUFFICIENT_TROOPS: '部队数量不足',
    INVALID_TERRAIN_TYPE: '无效的地形类型',
    DING_NOT_SPAWNED: '九鼎尚未刷新',
    DING_ALREADY_CONTROLLED: '九鼎已被其他玩家控制',
    MAP_DATA_CORRUPTED: '地图数据损坏，请刷新重试'
}
```

## 性能优化

### 1. 数据库优化
- **坐标索引**：为x,y坐标建立复合索引
- **分区表**：按地图区域分区存储
- **缓存策略**：热点区域数据缓存
- **批量查询**：九宫格数据批量加载

### 2. 前端渲染优化
- **Canvas分层**：地形、资源、UI分层渲染
- **视野裁剪**：只渲染可见区域
- **纹理缓存**：地形和资源纹理预加载
- **帧率控制**：限制渲染帧率，避免过度绘制

### 3. 网络优化
- **数据压缩**：地图数据gzip压缩传输
- **增量更新**：只传输变化的地图数据
- **预加载**：预加载周边区域数据
- **WebSocket**：实时资源状态更新

## 扩展预留

### 1. 建筑系统集成接口
```typescript
// 预留建筑系统集成接口
export interface BuildingSystemIntegration {
    canBuildAt(x: number, y: number, buildingType: string): Promise<boolean>
    placeBuildingAt(x: number, y: number, buildingId: number): Promise<void>
    removeBuildingAt(x: number, y: number): Promise<void>
    getBuildingsInRegion(minX: number, minY: number, maxX: number, maxY: number): Promise<Building[]>
}
```

### 2. 战斗系统集成预留
```typescript
// 预留战斗系统集成接口
export interface BattleSystemIntegration {
    canAttackAt(x: number, y: number): Promise<boolean>
    startBattleAt(x: number, y: number, attackerData: any): Promise<Battle>
    getTerrainBattleModifiers(terrainType: TerrainType): BattleModifiers
    applyTerrainEffects(battle: Battle, terrain: MapTile): Promise<void>
}
```

### 3. 联盟系统集成预留
```typescript
// 预留联盟系统集成接口
export interface AllianceSystemIntegration {
    getAllianceTerritories(allianceId: number): Promise<MapCoordinate[]>
    canAllianceMemberAccessTile(userId: number, x: number, y: number): Promise<boolean>
    shareResourceWithAlliance(resourceId: number, allianceId: number): Promise<void>
}
```

## 测试用例

### 1. 地图基础功能测试
- 地图初始化测试
- 坐标系统测试
- 地形生成测试
- 九宫格渲染测试

### 2. 资源系统测试
- 资源刷新机制测试
- 资源采集功能测试
- 资源等级分布测试
- 九鼎控制测试

### 3. 性能测试
- 大量玩家同时访问测试
- 地图数据加载性能测试
- 渲染帧率测试
- 内存使用测试

### 4. 边界条件测试
- 地图边界处理测试
- 坐标越界测试
- 资源数量上限测试
- 网络异常处理测试

这个开发文档涵盖了您策划文档中的所有地图系统需求，包括5000×5000地图、地形系统、资源动态刷新、九宫格渲染优化等功能，并为后续的建筑系统、战斗系统和联盟系统预留了集成接口。文档提供了完整的数据库设计、API接口、前端实现和配置管理方案，可以作为开发的详细指导。
