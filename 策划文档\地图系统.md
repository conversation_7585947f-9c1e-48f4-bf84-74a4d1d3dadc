# 地图系统
## 地图
- 地图是二维格子，5000*5000（暂定，根据测试情况增加或减少）
- 每个格子都有一个坐标，坐标是唯一的，坐标由x和y组成，x和y都是整数，从0开始，到5000结束，例如：[0,0]、[0,1]、[0,2]、[0,3]、[0,4]、[0,5]、[0,6]、[0,7]、[0,8]、[0,9]……等等
- 格子大小需要方便玩家在手机屏幕上用手指点击，需要你给我一个建议值
- 基于优化，格子渲染只渲染九宫格，即：以手指点击的格子为中心，渲染其周围的8个屏幕格子，共9屏格子，当手指移动到新的格子时，再重新渲染新的九宫格，以此类推，但是格子的坐标是不能变的，哪怕渲染的格子不在视野内，其坐标依旧存在。每屏以1080*1920为标准

- 格子分为1-10级

## 地形：开服时地形初始化后，将显示在地图的格子属性中，直到统一后，地形都不会再改变，重新开服后，地形将重新初始化分布。（静态刷地形）

### 地形包括以下10种，每种地形对玩家出征部队的兵种行军速度、以及战斗的兵种相生相克，以及战斗的策略应用，是有影响的，具体影响我还没想好，但需要保留功能及配置。
- 平原
- 草地
- 森林
- 雪地
- 湿地
- 沼泽
- 山地
- 荒漠
- 河流
- 栈道

## 资源
### 资源是动态随机刷新在地图格子上
- 资源是随机刷在格子上
- 资源刷新也是随机
- 资源刷新总量固定，但刷新的坐标格子，以及刷新的时间是随机的（例如：假如规定要刷1000个资源，这些资源分布在地图格子中，当某个资源被玩家取走了，那么就会隔5-10分钟，在地图随机某个格子刷出，资源满足1000个时，则不会再刷新，如果有100个资源，在1小时内依次被玩家取走，那么就会依次在地图上随机刷新出这些资源会，不会同时一起刷，降低服务器压力）
- 虽然说是随机，但是是可控范围的随机，比如1级资源只会出现在1级格子内，2级资源只会出现在2级格子内等等。

### 资源类型
- 资源分为矿石、森林、动物、流寇、九鼎五种
- 矿石产出矿石
- 森林产出木头
- 动物产出食物
- 流寇产出战国币及各种物品
- 九鼎产出：还在考虑
- 这些资源也分等级，等级越高，资源含量就越多，相应玩家采集获得也就越多。
- 资源也有各种形态，比如动物，就有羊、马、牛等，本质是一样的，产出食物。
- 九鼎只有九个，每种鼎各一个，九鼎是10级，其他的资源都有1-10级的等级。

### 服务端功能+前端功能+数据库都需要做